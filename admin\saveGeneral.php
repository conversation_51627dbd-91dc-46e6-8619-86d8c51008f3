<?php
/**
 * حفظ الإعدادات العامة
 * Save general settings
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__ . '/..');
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/models/SystemSetting.php';
require_once APP_PATH . '/core/AuthGuard.php';

// التحقق من صلاحيات الأدمن
adminOnly();

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    $settingsModel = new SystemSetting();
    
    $settings = [
        'site_name' => $_POST['site_name'] ?? '',
        'site_url' => $_POST['site_url'] ?? '',
        'site_description' => $_POST['site_description'] ?? '',
        'default_language' => $_POST['default_language'] ?? 'ar',
        'maintenance_mode' => isset($_POST['maintenance_mode']) ? 1 : 0,
        'allow_registration' => isset($_POST['allow_registration']) ? 1 : 0
    ];
    
    if ($settingsModel->saveSettings($settings)) {
        echo json_encode(['success' => true, 'message' => 'تم حفظ الإعدادات العامة بنجاح']);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حفظ الإعدادات']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
