<?php
/**
 * كنترولر إعدادات النظام للأدمن
 * Admin System Settings Controller
 */

class AdminSettingsController extends Controller
{
    public function __construct()
    {
        parent::__construct();
        adminOnly();
    }

    /**
     * صفحة الإعدادات الرئيسية
     * Main settings page
     */
    public function index()
    {
        $settingsModel = $this->model('SystemSetting');
        
        // جلب الإعدادات حسب الفئات
        $generalSettings = $settingsModel->getSettingsByCategory('general');
        $seoSettings = $settingsModel->getSettingsByCategory('seo');
        $appearanceSettings = $settingsModel->getSettingsByCategory('appearance');
        $emailSettings = $settingsModel->getSettingsByCategory('email');
        $securitySettings = $settingsModel->getSettingsByCategory('security');
        
        $data = [
            'title' => 'إعدادات النظام - لوحة تحكم الأدمن',
            'general' => $generalSettings,
            'seo' => $seoSettings,
            'appearance' => $appearanceSettings,
            'email' => $emailSettings,
            'security' => $securitySettings
        ];
        
        $this->view('admin/settings', $data);
    }
    
    /**
     * حفظ الإعدادات العامة
     * Save general settings
     */
    public function saveGeneral()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'طريقة غير مسموحة']);
            return;
        }
        
        $settingsModel = $this->model('SystemSetting');
        
        $settings = [
            'site_name' => $_POST['site_name'] ?? '',
            'site_url' => $_POST['site_url'] ?? '',
            'site_description' => $_POST['site_description'] ?? '',
            'default_language' => $_POST['default_language'] ?? 'ar',
            'maintenance_mode' => isset($_POST['maintenance_mode']) ? 1 : 0,
            'allow_registration' => isset($_POST['allow_registration']) ? 1 : 0
        ];
        
        if ($settingsModel->saveSettings($settings)) {
            $this->jsonResponse(['success' => true, 'message' => 'تم حفظ الإعدادات العامة بنجاح']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'حدث خطأ أثناء حفظ الإعدادات']);
        }
    }
    
    /**
     * حفظ إعدادات SEO
     * Save SEO settings
     */
    public function saveSeo()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'طريقة غير مسموحة']);
            return;
        }
        
        $settingsModel = $this->model('SystemSetting');
        
        $settings = [
            'meta_title' => $_POST['meta_title'] ?? '',
            'meta_description' => $_POST['meta_description'] ?? '',
            'google_analytics' => $_POST['google_analytics'] ?? '',
            'enable_sitemap' => isset($_POST['enable_sitemap']) ? 1 : 0
        ];
        
        if ($settingsModel->saveSettings($settings)) {
            $this->jsonResponse(['success' => true, 'message' => 'تم حفظ إعدادات SEO بنجاح']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'حدث خطأ أثناء حفظ الإعدادات']);
        }
    }
    
    /**
     * حفظ إعدادات المظهر
     * Save appearance settings
     */
    public function saveAppearance()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'طريقة غير مسموحة']);
            return;
        }
        
        $settingsModel = $this->model('SystemSetting');
        
        $settings = [
            'primary_color' => $_POST['primary_color'] ?? '#007BFF',
            'secondary_color' => $_POST['secondary_color'] ?? '#6c757d'
        ];
        
        // معالجة رفع الملفات (الشعار والأيقونة)
        if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === UPLOAD_ERR_OK) {
            $logoPath = $this->uploadFile($_FILES['site_logo'], 'logos');
            if ($logoPath) {
                $settings['site_logo'] = $logoPath;
            }
        }
        
        if (isset($_FILES['site_favicon']) && $_FILES['site_favicon']['error'] === UPLOAD_ERR_OK) {
            $faviconPath = $this->uploadFile($_FILES['site_favicon'], 'icons');
            if ($faviconPath) {
                $settings['site_favicon'] = $faviconPath;
            }
        }
        
        if ($settingsModel->saveSettings($settings)) {
            $this->jsonResponse(['success' => true, 'message' => 'تم حفظ إعدادات المظهر بنجاح']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'حدث خطأ أثناء حفظ الإعدادات']);
        }
    }
    
    /**
     * حفظ إعدادات البريد الإلكتروني
     * Save email settings
     */
    public function saveEmail()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'طريقة غير مسموحة']);
            return;
        }
        
        $settingsModel = $this->model('SystemSetting');
        
        $settings = [
            'smtp_host' => $_POST['smtp_host'] ?? '',
            'smtp_port' => (int)($_POST['smtp_port'] ?? 587),
            'smtp_username' => $_POST['smtp_username'] ?? '',
            'smtp_password' => $_POST['smtp_password'] ?? ''
        ];
        
        if ($settingsModel->saveSettings($settings)) {
            $this->jsonResponse(['success' => true, 'message' => 'تم حفظ إعدادات البريد الإلكتروني بنجاح']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'حدث خطأ أثناء حفظ الإعدادات']);
        }
    }
    
    /**
     * حفظ إعدادات الأمان
     * Save security settings
     */
    public function saveSecurity()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'طريقة غير مسموحة']);
            return;
        }
        
        $settingsModel = $this->model('SystemSetting');
        
        $settings = [
            'max_login_attempts' => (int)($_POST['max_login_attempts'] ?? 5),
            'lockout_duration' => (int)($_POST['lockout_duration'] ?? 15),
            'enable_2fa' => isset($_POST['enable_2fa']) ? 1 : 0,
            'force_https' => isset($_POST['force_https']) ? 1 : 0
        ];
        
        if ($settingsModel->saveSettings($settings)) {
            $this->jsonResponse(['success' => true, 'message' => 'تم حفظ إعدادات الأمان بنجاح']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'حدث خطأ أثناء حفظ الإعدادات']);
        }
    }
    
    /**
     * اختبار البريد الإلكتروني
     * Test email
     */
    public function testEmail()
    {
        $settingsModel = $this->model('SystemSetting');
        $emailSettings = $settingsModel->getEmailSettings();
        
        // في التطبيق الحقيقي، سيتم إرسال بريد اختبار
        $this->jsonResponse(['success' => true, 'message' => 'تم إرسال بريد اختبار، تحقق من صندوق الوارد']);
    }
    
    /**
     * رفع ملف
     * Upload file
     */
    private function uploadFile($file, $folder)
    {
        $uploadDir = UPLOAD_PATH . '/' . $folder;
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $fileName = time() . '_' . $file['name'];
        $filePath = $uploadDir . '/' . $fileName;
        
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            return 'uploads/' . $folder . '/' . $fileName;
        }
        
        return false;
    }
    
    /**
     * إرسال استجابة JSON
     * Send JSON response
     */
    private function jsonResponse($data)
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}
?>
