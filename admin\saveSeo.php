<?php
/**
 * حفظ إعدادات SEO
 * Save SEO settings
 */

// تشغيل الجلسات
session_start();

// تعريف المسارات
define('ROOT_PATH', __DIR__ . '/..');
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل الإعدادات
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/models/SystemSetting.php';
require_once APP_PATH . '/core/AuthGuard.php';

// التحقق من صلاحيات الأدمن
adminOnly();

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    $settingsModel = new SystemSetting();
    
    $settings = [
        'meta_title' => $_POST['meta_title'] ?? '',
        'meta_description' => $_POST['meta_description'] ?? '',
        'google_analytics' => $_POST['google_analytics'] ?? '',
        'enable_sitemap' => isset($_POST['enable_sitemap']) ? 1 : 0
    ];
    
    if ($settingsModel->saveSettings($settings)) {
        echo json_encode(['success' => true, 'message' => 'تم حفظ إعدادات SEO بنجاح']);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حفظ الإعدادات']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
