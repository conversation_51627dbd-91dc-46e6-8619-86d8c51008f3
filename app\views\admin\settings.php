<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $data['title'] ?? 'الإعدادات - لوحة تحكم الأدمن' ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../public/favicon.svg">
    <link rel="icon" type="image/x-icon" href="../public/favicon.ico">
    <link rel="apple-touch-icon" href="../public/favicon.svg">
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #007BFF;
            --primary-white: #FFFFFF;
            --primary-black: #333333;
            --light-gray: #F8F9FA;
            --admin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--light-gray);
        }

        .sidebar {
            min-height: 100vh;
            background: var(--admin-gradient);
            color: var(--primary-white);
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            overflow-y: auto;
            max-height: 100vh;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px 15px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
            text-decoration: none;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.15);
            color: var(--primary-white);
            transform: translateX(-3px);
            border-color: rgba(255,255,255,0.2);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-white);
            color: var(--primary-blue);
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .main-content {
            margin-right: 250px;
            padding: 25px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }

        .card-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border-radius: 0 !important;
            border: none;
            padding: 20px;
            font-weight: 600;
        }

        .admin-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .nav-tabs {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 25px;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px 10px 0 0;
            margin-right: 5px;
            padding: 12px 20px;
            color: #666;
            font-weight: 500;
        }

        .nav-tabs .nav-link.active {
            background: var(--admin-gradient);
            color: white;
            border: none;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: var(--primary-black);
            margin-bottom: 8px;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .btn-primary {
            background: var(--admin-gradient);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--success-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .color-picker {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    لوحة تحكم الأدمن
                </h4>
                <small class="text-white-50">نظام إدارة العيادات</small>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php" data-page="dashboard">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    الرئيسية
                </a>
                <a class="nav-link" href="clinics.php" data-page="clinics">
                    <i class="fas fa-hospital me-2"></i>
                    العيادات
                </a>
                <a class="nav-link" href="subscriptions.php" data-page="subscriptions">
                    <i class="fas fa-credit-card me-2"></i>
                    الاشتراكات
                </a>
                <a class="nav-link" href="users.php" data-page="users">
                    <i class="fas fa-users me-2"></i>
                    المستخدمين
                </a>
                <a class="nav-link" href="revenue.php" data-page="revenue">
                    <i class="fas fa-chart-line me-2"></i>
                    الإيرادات
                </a>
                <a class="nav-link" href="messages.php" data-page="messages">
                    <i class="fas fa-envelope me-2"></i>
                    الرسائل
                </a>
                <a class="nav-link" href="settings.php" data-page="settings">
                    <i class="fas fa-cog me-2"></i>
                    الإعدادات
                </a>
                <hr class="text-white-50">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    تسجيل الخروج
                </a>
            </nav>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="admin-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-cog me-3"></i>
                        إعدادات النظام
                    </h1>
                    <p class="mb-0 mt-2">تكوين وإدارة إعدادات النظام الأساسية</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات النظام
                </h5>
            </div>
            <div class="card-body">
                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                            <i class="fas fa-cog me-2"></i>عام
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="seo-tab" data-bs-toggle="tab" data-bs-target="#seo" type="button" role="tab">
                            <i class="fas fa-search me-2"></i>SEO
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab">
                            <i class="fas fa-palette me-2"></i>المظهر
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                            <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                            <i class="fas fa-shield-alt me-2"></i>الأمان
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="settingsTabContent">
                    <!-- General Settings -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel">
                        <form id="generalSettingsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">اسم الموقع</label>
                                        <input type="text" class="form-control" name="site_name" value="<?= htmlspecialchars($data['general']['site_name'] ?? 'نظام حكيم') ?>" placeholder="اسم الموقع">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">عنوان الموقع</label>
                                        <input type="url" class="form-control" name="site_url" value="<?= htmlspecialchars($data['general']['site_url'] ?? 'https://bujairi.shop') ?>" placeholder="https://example.com">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">وصف الموقع</label>
                                        <textarea class="form-control" name="site_description" rows="3" placeholder="وصف مختصر عن الموقع"><?= htmlspecialchars($data['general']['site_description'] ?? 'نظام إدارة العيادات الطبية المتكامل') ?></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">اللغة الافتراضية</label>
                                        <select class="form-select" name="default_language">
                                            <option value="ar" <?= ($data['general']['default_language'] ?? 'ar') === 'ar' ? 'selected' : '' ?>>العربية</option>
                                            <option value="en" <?= ($data['general']['default_language'] ?? 'ar') === 'en' ? 'selected' : '' ?>>English</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">تفعيل الصيانة</label>
                                        <div class="d-flex align-items-center">
                                            <label class="switch me-3">
                                                <input type="checkbox" name="maintenance_mode" <?= ($data['general']['maintenance_mode'] ?? false) ? 'checked' : '' ?>>
                                                <span class="slider"></span>
                                            </label>
                                            <span>تفعيل وضع الصيانة</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">تفعيل التسجيل</label>
                                        <div class="d-flex align-items-center">
                                            <label class="switch me-3">
                                                <input type="checkbox" name="allow_registration" <?= ($data['general']['allow_registration'] ?? true) ? 'checked' : '' ?>>
                                                <span class="slider"></span>
                                            </label>
                                            <span>السماح بالتسجيل الجديد</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات العامة
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- SEO Settings -->
                    <div class="tab-pane fade" id="seo" role="tabpanel">
                        <form id="seoSettingsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Meta Title</label>
                                        <input type="text" class="form-control" name="meta_title" value="<?= htmlspecialchars($data['seo']['meta_title'] ?? 'نظام حكيم - إدارة العيادات الطبية') ?>" placeholder="عنوان الصفحة">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Meta Description</label>
                                        <textarea class="form-control" name="meta_description" rows="2" placeholder="وصف الصفحة"><?= htmlspecialchars($data['seo']['meta_description'] ?? 'نظام متكامل لإدارة العيادات الطبية والمريضات') ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Google Analytics Code</label>
                                        <textarea class="form-control" name="google_analytics" rows="3" placeholder="كود Google Analytics"><?= htmlspecialchars($data['seo']['google_analytics'] ?? '') ?></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">تفعيل Site Map</label>
                                        <div class="d-flex align-items-center">
                                            <label class="switch me-3">
                                                <input type="checkbox" name="enable_sitemap" <?= ($data['seo']['enable_sitemap'] ?? true) ? 'checked' : '' ?>>
                                                <span class="slider"></span>
                                            </label>
                                            <span>إنشاء ملف Site Map تلقائياً</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>ملاحظة:</strong> يمكنك الوصول لملف Site Map عبر: 
                                <a href="sitemap.xml" target="_blank">sitemap.xml</a>
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ إعدادات SEO
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Appearance Settings -->
                    <div class="tab-pane fade" id="appearance" role="tabpanel">
                        <form id="appearanceSettingsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">اللون الأساسي</label>
                                        <input type="color" class="color-picker" name="primary_color" value="<?= htmlspecialchars($data['appearance']['primary_color'] ?? '#007BFF') ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">اللون الثانوي</label>
                                        <input type="color" class="color-picker" name="secondary_color" value="<?= htmlspecialchars($data['appearance']['secondary_color'] ?? '#6c757d') ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">شعار الموقع</label>
                                        <input type="file" class="form-control" name="site_logo" accept="image/*">
                                        <small class="text-muted">الأبعاد الموصى بها: 200×60 بكسل</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">أيقونة الموقع (Favicon)</label>
                                        <input type="file" class="form-control" name="site_favicon" accept="image/*">
                                        <small class="text-muted">الأبعاد الموصى بها: 32×32 بكسل</small>
                                    </div>
                                </div>
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ إعدادات المظهر
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Email Settings -->
                    <div class="tab-pane fade" id="email" role="tabpanel">
                        <form id="emailSettingsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">خادم SMTP</label>
                                        <input type="text" class="form-control" name="smtp_host" value="<?= htmlspecialchars($data['email']['smtp_host'] ?? 'smtp.gmail.com') ?>" placeholder="smtp.gmail.com">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">منفذ SMTP</label>
                                        <input type="number" class="form-control" name="smtp_port" value="<?= htmlspecialchars($data['email']['smtp_port'] ?? '587') ?>" placeholder="587">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">اسم المستخدم</label>
                                        <input type="email" class="form-control" name="smtp_username" value="<?= htmlspecialchars($data['email']['smtp_username'] ?? '') ?>" placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" name="smtp_password" value="<?= htmlspecialchars($data['email']['smtp_password'] ?? '') ?>" placeholder="كلمة مرور التطبيق">
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تنبيه:</strong> تأكد من استخدام كلمة مرور التطبيق وليس كلمة مرور الحساب العادية
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ إعدادات البريد
                                </button>
                                <button type="button" class="btn btn-outline-primary ms-2" onclick="testEmail()">
                                    <i class="fas fa-paper-plane me-2"></i>اختبار البريد
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Security Settings -->
                    <div class="tab-pane fade" id="security" role="tabpanel">
                        <form id="securitySettingsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">الحد الأقصى لمحاولات تسجيل الدخول</label>
                                        <input type="number" class="form-control" name="max_login_attempts" value="<?= htmlspecialchars($data['security']['max_login_attempts'] ?? '5') ?>" min="3" max="10">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">مدة حظر الحساب (بالدقائق)</label>
                                        <input type="number" class="form-control" name="lockout_duration" value="<?= htmlspecialchars($data['security']['lockout_duration'] ?? '15') ?>" min="5" max="60">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">تفعيل المصادقة الثنائية</label>
                                        <div class="d-flex align-items-center">
                                            <label class="switch me-3">
                                                <input type="checkbox" name="enable_2fa" <?= ($data['security']['enable_2fa'] ?? false) ? 'checked' : '' ?>>
                                                <span class="slider"></span>
                                            </label>
                                            <span>تفعيل المصادقة الثنائية للمستخدمين</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">تفعيل HTTPS</label>
                                        <div class="d-flex align-items-center">
                                            <label class="switch me-3">
                                                <input type="checkbox" name="force_https" <?= ($data['security']['force_https'] ?? true) ? 'checked' : '' ?>>
                                                <span class="slider"></span>
                                            </label>
                                            <span>إجبار استخدام HTTPS</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ إعدادات الأمان
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تفعيل القائمة الجانبية
        function activateNavLink() {
            const currentPage = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');
                
                // استخراج اسم الملف من الرابط
                const fileName = href.split('/').pop();
                const currentFileName = currentPage.split('/').pop();
                
                // التحقق من تطابق اسم الملف
                if (fileName && fileName === currentFileName) {
                    link.classList.add('active');
                }
            });
        }

        // تبديل القائمة الجانبية (للموبايل)
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // حفظ الإعدادات العامة
        document.getElementById('generalSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveSettings('general', this);
        });

        // حفظ إعدادات SEO
        document.getElementById('seoSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveSettings('seo', this);
        });

        // حفظ إعدادات المظهر
        document.getElementById('appearanceSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveSettings('appearance', this);
        });

        // حفظ إعدادات البريد
        document.getElementById('emailSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveSettings('email', this);
        });

        // حفظ إعدادات الأمان
        document.getElementById('securitySettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveSettings('security', this);
        });

        function saveSettings(type, form) {
            const formData = new FormData(form);

            // إرسال البيانات للخادم
            fetch(`save${type.charAt(0).toUpperCase() + type.slice(1)}.php`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('حدث خطأ أثناء حفظ الإعدادات', 'danger');
            });
        }

        function testEmail() {
            fetch('testEmail.php', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'info');
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('حدث خطأ أثناء اختبار البريد الإلكتروني', 'danger');
            });
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.querySelector('.main-content').insertBefore(alertDiv, document.querySelector('.card'));
            
            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // تفعيل القائمة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            activateNavLink();
        });
    </script>
</body>
</html> 