<?php
/**
 * نموذج إعدادات النظام
 * System Settings Model
 */

class SystemSetting extends Model
{
    protected $table = 'system_settings';
    
    /**
     * الحصول على إعداد بواسطة المفتاح
     * Get setting by key
     */
    public function getSetting($key, $default = null)
    {
        $sql = "SELECT setting_value, setting_type FROM {$this->table} WHERE setting_key = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$key]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            return $default;
        }
        
        return $this->castValue($result['setting_value'], $result['setting_type']);
    }
    
    /**
     * تحديث إعداد
     * Update setting
     */
    public function setSetting($key, $value, $type = 'string')
    {
        // تحويل القيمة إلى نص للحفظ
        $stringValue = $this->valueToString($value, $type);
        
        $sql = "INSERT INTO {$this->table} (setting_key, setting_value, setting_type, updated_at) 
                VALUES (?, ?, ?, NOW()) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                setting_type = VALUES(setting_type),
                updated_at = NOW()";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$key, $stringValue, $type]);
    }
    
    /**
     * الحصول على جميع الإعدادات حسب الفئة
     * Get all settings by category
     */
    public function getSettingsByCategory($category)
    {
        $sql = "SELECT setting_key, setting_value, setting_type FROM {$this->table} WHERE category = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $settings = [];
        foreach ($results as $result) {
            $settings[$result['setting_key']] = $this->castValue($result['setting_value'], $result['setting_type']);
        }
        
        return $settings;
    }
    
    /**
     * الحصول على جميع الإعدادات العامة
     * Get all public settings
     */
    public function getPublicSettings()
    {
        $sql = "SELECT setting_key, setting_value, setting_type FROM {$this->table} WHERE is_public = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $settings = [];
        foreach ($results as $result) {
            $settings[$result['setting_key']] = $this->castValue($result['setting_value'], $result['setting_type']);
        }
        
        return $settings;
    }
    
    /**
     * حفظ إعدادات متعددة
     * Save multiple settings
     */
    public function saveSettings($settings)
    {
        $this->db->beginTransaction();
        
        try {
            foreach ($settings as $key => $value) {
                // تحديد نوع البيانات تلقائياً
                $type = $this->detectType($value);
                $this->setSetting($key, $value, $type);
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * تحويل القيمة حسب النوع
     * Cast value by type
     */
    private function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return (bool) $value;
            case 'integer':
                return (int) $value;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }
    
    /**
     * تحويل القيمة إلى نص للحفظ
     * Convert value to string for storage
     */
    private function valueToString($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'json':
                return json_encode($value);
            default:
                return (string) $value;
        }
    }
    
    /**
     * تحديد نوع البيانات تلقائياً
     * Auto-detect data type
     */
    private function detectType($value)
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_array($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }
    
    /**
     * التحقق من وضع الصيانة
     * Check maintenance mode
     */
    public function isMaintenanceMode()
    {
        return $this->getSetting('maintenance_mode', false);
    }
    
    /**
     * تفعيل وضع الصيانة
     * Enable maintenance mode
     */
    public function enableMaintenanceMode()
    {
        return $this->setSetting('maintenance_mode', true, 'boolean');
    }
    
    /**
     * إلغاء تفعيل وضع الصيانة
     * Disable maintenance mode
     */
    public function disableMaintenanceMode()
    {
        return $this->setSetting('maintenance_mode', false, 'boolean');
    }
    
    /**
     * الحصول على إعدادات البريد الإلكتروني
     * Get email settings
     */
    public function getEmailSettings()
    {
        return $this->getSettingsByCategory('email');
    }
    
    /**
     * الحصول على إعدادات الأمان
     * Get security settings
     */
    public function getSecuritySettings()
    {
        return $this->getSettingsByCategory('security');
    }
}
?>
