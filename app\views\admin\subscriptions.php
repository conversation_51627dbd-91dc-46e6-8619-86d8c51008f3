<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $data['title'] ?? 'إدارة الاشتراكات - لوحة تحكم الأدمن' ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../public/favicon.svg">
    <link rel="icon" type="image/x-icon" href="../public/favicon.ico">
    <link rel="apple-touch-icon" href="../public/favicon.svg">
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #007BFF;
            --primary-sky-blue: #87CEEB;
            --primary-white: #FFFFFF;
            --primary-black: #333333;
            --light-gray: #F8F9FA;
            --border-color: #E0E0E0;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
            --admin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--light-gray);
            color: var(--primary-black);
        }

        .sidebar {
            min-height: 100vh;
            background: var(--admin-gradient);
            color: var(--primary-white);
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            overflow-y: auto;
            max-height: 100vh;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px 15px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
            text-decoration: none;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.15);
            color: var(--primary-white);
            transform: translateX(-3px);
            border-color: rgba(255,255,255,0.2);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-white);
            color: var(--primary-blue);
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .main-content {
            margin-right: 250px;
            padding: 25px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-white);
            box-shadow: var(--shadow);
            margin-bottom: 25px;
            border-radius: 10px;
            padding: 15px 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: var(--shadow);
            margin-bottom: 25px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-2px);
        }

        .card-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border-radius: 0 !important;
            border: none;
            padding: 20px;
            font-weight: 600;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .table th {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border: none;
            font-weight: 600;
            padding: 15px;
        }

        .table td {
            padding: 15px;
            border-color: var(--border-color);
            vertical-align: middle;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
        }

        .badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.8rem;
        }

        .btn-primary {
            background: var(--admin-gradient);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .stats-card {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .stats-card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-3px);
        }

        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stats-card .stats-icon {
            opacity: 0.8;
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .subscription-status {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
        }

        .status-active {
            background-color: var(--success-color);
            color: white;
        }

        .status-expired {
            background-color: var(--danger-color);
            color: white;
        }

        .status-expiring {
            background-color: var(--warning-color);
            color: var(--primary-black);
        }

        .admin-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    لوحة تحكم الأدمن
                </h4>
                <small class="text-white-50">نظام إدارة العيادات</small>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php" data-page="dashboard">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    الرئيسية
                </a>
                <a class="nav-link" href="clinics.php" data-page="clinics">
                    <i class="fas fa-hospital me-2"></i>
                    العيادات
                </a>
                <a class="nav-link" href="subscriptions.php" data-page="subscriptions">
                    <i class="fas fa-credit-card me-2"></i>
                    الاشتراكات
                </a>
                <a class="nav-link" href="users.php" data-page="users">
                    <i class="fas fa-users me-2"></i>
                    المستخدمين
                </a>
                <a class="nav-link" href="revenue.php" data-page="revenue">
                    <i class="fas fa-chart-line me-2"></i>
                    الإيرادات
                </a>
                <a class="nav-link" href="messages.php" data-page="messages">
                    <i class="fas fa-envelope me-2"></i>
                    الرسائل
                </a>
                <a class="nav-link" href="settings.php" data-page="settings">
                    <i class="fas fa-cog me-2"></i>
                    الإعدادات
                </a>
                <hr class="text-white-50">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    تسجيل الخروج
                </a>
            </nav>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="admin-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-credit-card me-3"></i>
                        إدارة الاشتراكات
                    </h1>
                    <p class="mb-0 mt-2">مراقبة وإدارة اشتراكات العيادات</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-credit-card stats-icon"></i>
                    <div class="stats-number"><?= $data['expired_clinics'] ? count($data['expired_clinics']) : 0 ?></div>
                    <div>الاشتراكات المنتهية</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-exclamation-triangle stats-icon"></i>
                    <div class="stats-number"><?= $data['expiring_soon'] ? count($data['expiring_soon']) : 0 ?></div>
                    <div>تنتهي قريباً</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-check-circle stats-icon"></i>
                    <div class="stats-number"><?= isset($data['active_subscriptions']) ? $data['active_subscriptions'] : 0 ?></div>
                    <div>الاشتراكات النشطة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-percentage stats-icon"></i>
                    <div class="stats-number">85%</div>
                    <div>معدل التجديد</div>
                </div>
            </div>
        </div>

        <!-- Expired Subscriptions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    الاشتراكات المنتهية
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($data['expired_clinics'])): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم العيادة</th>
                                    <th>المالك</th>
                                    <th>تاريخ انتهاء الاشتراك</th>
                                    <th>نوع الاشتراك</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($data['expired_clinics'] as $clinic): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($clinic['name'] ?? 'غير محدد') ?></strong>
                                        </td>
                                        <td><?= htmlspecialchars($clinic['owner_name'] ?? 'غير محدد') ?></td>
                                        <td>
                                            <span class="text-danger">
                                                <?= isset($clinic['subscription_end']) ? date('Y-m-d', strtotime($clinic['subscription_end'])) : 'غير محدد' ?>
                                            </span>
                                        </td>
                                        <td><?= htmlspecialchars($clinic['subscription_type'] ?? 'غير محدد') ?></td>
                                        <td>
                                            <span class="subscription-status status-expired">منتهي</span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="renewSubscription(<?= $clinic['id'] ?? 0 ?>)">
                                                <i class="fas fa-sync-alt me-1"></i>
                                                تجديد
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="contactOwner(<?= $clinic['id'] ?? 0 ?>)">
                                                <i class="fas fa-envelope me-1"></i>
                                                تواصل
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">لا توجد اشتراكات منتهية</h5>
                        <p class="text-muted">جميع الاشتراكات نشطة ومحدثة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Expiring Soon -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    الاشتراكات التي تنتهي قريباً (خلال 7 أيام)
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($data['expiring_soon'])): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم العيادة</th>
                                    <th>المالك</th>
                                    <th>تاريخ انتهاء الاشتراك</th>
                                    <th>الأيام المتبقية</th>
                                    <th>نوع الاشتراك</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($data['expiring_soon'] as $clinic): ?>
                                    <?php 
                                    $endDate = isset($clinic['subscription_end']) ? strtotime($clinic['subscription_end']) : 0;
                                    $daysLeft = $endDate ? ceil(($endDate - time()) / (60 * 60 * 24)) : 0;
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($clinic['name'] ?? 'غير محدد') ?></strong>
                                        </td>
                                        <td><?= htmlspecialchars($clinic['owner_name'] ?? 'غير محدد') ?></td>
                                        <td>
                                            <span class="text-warning">
                                                <?= $endDate ? date('Y-m-d', $endDate) : 'غير محدد' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning text-dark">
                                                <?= $daysLeft ?> يوم
                                            </span>
                                        </td>
                                        <td><?= htmlspecialchars($clinic['subscription_type'] ?? 'غير محدد') ?></td>
                                        <td>
                                            <span class="subscription-status status-expiring">ينتهي قريباً</span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-warning" onclick="sendReminder(<?= $clinic['id'] ?? 0 ?>)">
                                                <i class="fas fa-bell me-1"></i>
                                                تذكير
                                            </button>
                                            <button class="btn btn-sm btn-primary" onclick="renewSubscription(<?= $clinic['id'] ?? 0 ?>)">
                                                <i class="fas fa-sync-alt me-1"></i>
                                                تجديد
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-thumbs-up text-success" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">لا توجد اشتراكات تنتهي قريباً</h5>
                        <p class="text-muted">جميع الاشتراكات آمنة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Subscription Plans -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    خطط الاشتراك المتاحة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white text-center">
                                <h5 class="mb-0">الخطة الأساسية</h5>
                            </div>
                            <div class="card-body text-center">
                                <h3 class="text-primary">$29</h3>
                                <p class="text-muted">شهرياً</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>إدارة المرضى</li>
                                    <li><i class="fas fa-check text-success me-2"></i>المواعيد</li>
                                    <li><i class="fas fa-check text-success me-2"></i>التقارير الأساسية</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark text-center">
                                <h5 class="mb-0">الخطة المتقدمة</h5>
                            </div>
                            <div class="card-body text-center">
                                <h3 class="text-warning">$59</h3>
                                <p class="text-muted">شهرياً</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>جميع ميزات الخطة الأساسية</li>
                                    <li><i class="fas fa-check text-success me-2"></i>الوصفات الطبية</li>
                                    <li><i class="fas fa-check text-success me-2"></i>التقارير المتقدمة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>دعم فني</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white text-center">
                                <h5 class="mb-0">الخطة الاحترافية</h5>
                            </div>
                            <div class="card-body text-center">
                                <h3 class="text-success">$99</h3>
                                <p class="text-muted">شهرياً</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>جميع الميزات</li>
                                    <li><i class="fas fa-check text-success me-2"></i>إدارة متعددة العيادات</li>
                                    <li><i class="fas fa-check text-success me-2"></i>API مخصص</li>
                                    <li><i class="fas fa-check text-success me-2"></i>دعم فني 24/7</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل القائمة الجانبية
        function activateNavLink() {
            const currentPage = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');
                
                // استخراج اسم الملف من الرابط
                const fileName = href.split('/').pop();
                const currentFileName = currentPage.split('/').pop();
                
                // التحقق من تطابق اسم الملف
                if (fileName && fileName === currentFileName) {
                    link.classList.add('active');
                }
            });
        }

        // تبديل القائمة الجانبية (للموبايل)
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        function renewSubscription(clinicId) {
            if (confirm('هل تريد تجديد اشتراك هذه العيادة؟')) {
                // إرسال طلب تجديد الاشتراك
                console.log('تجديد اشتراك العيادة:', clinicId);
                alert('تم إرسال طلب تجديد الاشتراك');
            }
        }

        function contactOwner(clinicId) {
            // فتح نافذة التواصل مع مالك العيادة
            console.log('التواصل مع مالك العيادة:', clinicId);
            alert('سيتم فتح نافذة التواصل مع مالك العيادة');
        }

        function sendReminder(clinicId) {
            if (confirm('هل تريد إرسال تذكير لمالك العيادة؟')) {
                // إرسال تذكير
                console.log('إرسال تذكير للعيادة:', clinicId);
                alert('تم إرسال التذكير بنجاح');
            }
        }

        // تفعيل القائمة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            activateNavLink();
        });
    </script>
</body>
</html> 